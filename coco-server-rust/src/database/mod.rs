// SurrealDB数据库模块
pub mod client;
pub mod config;
pub mod migration;

pub use client::SurrealDBClient;
pub use config::DatabaseConfig;

// 全局数据库连接（参考SurrealDB官方文档）
use std::sync::LazyLock;
use surrealdb::{
    engine::remote::ws::{Client, Ws},
    opt::auth::Root,
    Surreal,
};

/// 全局数据库实例
pub static DB: LazyLock<Surreal<Client>> = LazyLock::new(Surreal::init);

/// 初始化全局数据库连接
pub async fn init_database(config: &DatabaseConfig) -> crate::error::Result<()> {
    // 连接到数据库
    DB.connect::<Ws>(&config.url)
        .await
        .map_err(|e| crate::error::Error::Database(format!("连接SurrealDB失败: {}", e)))?;

    // 使用root用户登录
    DB.signin(Root {
        username: &config.username,
        password: &config.password,
    })
    .await
    .map_err(|e| crate::error::Error::Database(format!("SurrealDB登录失败: {}", e)))?;

    // 选择命名空间和数据库
    DB.use_ns(&config.namespace)
        .use_db(&config.database)
        .await
        .map_err(|e| crate::error::Error::Database(format!("选择数据库失败: {}", e)))?;

    tracing::info!("全局数据库连接初始化成功");
    Ok(())
}
