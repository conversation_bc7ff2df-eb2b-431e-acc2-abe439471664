mod app_state;
mod auth;
mod config;
mod database;
mod error;
mod handlers;
mod health;
mod middleware;
mod models;
mod repositories;
mod services;
mod tls;

use axum::{
    routing::{delete, get, post},
    Router,
};
use config::config_manager::ConfigManager;
use handlers::info_handler::info_handler;
use handlers::setup_handler::initialize_handler;
use handlers::websocket_handler::websocket_handler;
use middleware::auth_middleware::auth_middleware;
use std::net::SocketAddr;
use std::sync::Arc;
use tower_http::cors::CorsLayer;
use tracing_subscriber;

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // 初始化日志系统
    tracing_subscriber::fmt::init();

    tracing::info!("Starting Coco Server with dual-port architecture...");

    // 加载配置
    let config_manager =
        ConfigManager::new().map_err(|e| anyhow::anyhow!("Failed to load configuration: {}", e))?;

    // 验证端口配置
    config_manager
        .validate_port_config()
        .map_err(|e| anyhow::anyhow!("Invalid port configuration: {}", e))?;

    // 记录配置信息
    let config = config_manager.get_config();
    tracing::info!("Configuration loaded successfully");

    // 记录环境配置信息
    if let Some(env_config) = &config.env {
        tracing::debug!("Environment configuration loaded");
        if let Some(es_endpoint) = &env_config.es_endpoint {
            tracing::info!("Elasticsearch endpoint: {}", es_endpoint);
        }
        if let Some(es_username) = &env_config.es_username {
            tracing::debug!("Elasticsearch username: {}", es_username);
        }
        if let Some(web_binding) = &env_config.web_binding {
            tracing::info!("Web binding: {}", web_binding);
        }
        if let Some(api_binding) = &env_config.api_binding {
            tracing::info!("API binding: {}", api_binding);
        }
    }

    // 记录服务器配置信息
    if let Some(coco_config) = &config.coco {
        if let Some(server_config) = &coco_config.server {
            tracing::debug!("Server configuration loaded");
            if let Some(public) = server_config.public {
                tracing::info!("Server public access: {}", public);
            }
            if let Some(name) = &server_config.name {
                tracing::info!("Server name: {}", name);
            }
        }
    }

    // 将配置管理器包装在Arc中以便在线程间共享
    let config_manager = Arc::new(config_manager);

    // 获取Web服务和API服务的绑定地址
    let web_addr = parse_binding_address(
        config_manager
            .get_web_binding()
            .as_deref()
            .unwrap_or("0.0.0.0:9000"),
    );
    let api_addr = parse_binding_address(
        config_manager
            .get_api_binding()
            .as_deref()
            .unwrap_or("0.0.0.0:2900"),
    );

    tracing::info!("Web service will bind to address: {}", web_addr);
    tracing::info!("API service will bind to address: {}", api_addr);

    // 记录TLS配置信息
    let use_https = std::env::var("USE_HTTPS").is_ok();
    if use_https {
        tracing::info!("HTTPS is enabled");
        let cert_path = std::env::var("CERT_PATH").unwrap_or_else(|_| "cert.pem".to_string());
        let key_path = std::env::var("KEY_PATH").unwrap_or_else(|_| "key.pem".to_string());
        tracing::debug!("Certificate path: {}, Key path: {}", cert_path, key_path);
    } else {
        tracing::info!("HTTPS is disabled, using HTTP");
    }

    // 启动Web服务和API服务
    let web_server_handle = tokio::spawn(start_web_server(
        config_manager.clone(),
        web_addr,
        use_https,
    ));
    let api_server_handle = tokio::spawn(start_api_server(
        config_manager.clone(),
        api_addr,
        use_https,
    ));

    // 等待任一服务完成（这通常意味着出现错误）
    // 使用futures::future::select来处理服务完成事件
    use futures::future::{select, Either};

    match select(web_server_handle, api_server_handle).await {
        Either::Left((web_result, api_server_handle)) => {
            match web_result {
                Ok(Ok(_)) => {
                    tracing::info!("Web server completed successfully");
                }
                Ok(Err(e)) => {
                    tracing::error!("Web server failed: {}", e);
                }
                Err(e) => {
                    tracing::error!("Web server task panicked: {}", e);
                }
            }

            // 继续等待API服务完成
            if let Ok(api_result) = api_server_handle.await {
                match api_result {
                    Ok(_) => tracing::info!("API server also completed successfully"),
                    Err(e) => tracing::error!("API server failed: {}", e),
                }
            } else {
                tracing::error!("API server task panicked");
            }
        }
        Either::Right((api_result, web_server_handle)) => {
            match api_result {
                Ok(Ok(_)) => {
                    tracing::info!("API server completed successfully");
                }
                Ok(Err(e)) => {
                    tracing::error!("API server failed: {}", e);
                }
                Err(e) => {
                    tracing::error!("API server task panicked: {}", e);
                }
            }

            // 继续等待Web服务完成
            if let Ok(web_result) = web_server_handle.await {
                match web_result {
                    Ok(_) => tracing::info!("Web server also completed successfully"),
                    Err(e) => tracing::error!("Web server failed: {}", e),
                }
            } else {
                tracing::error!("Web server task panicked");
            }
        }
    }

    tracing::info!("Server shutdown complete");

    Ok(())
}

/// 解析绑定地址字符串为SocketAddr
fn parse_binding_address(binding: &str) -> SocketAddr {
    // 绑定格式通常是 "0.0.0.0:9000" 或 ":9000"
    if let Some(colon_pos) = binding.rfind(':') {
        let ip_part = &binding[..colon_pos];
        let port_str = &binding[colon_pos + 1..];

        if let Ok(port) = port_str.parse::<u16>() {
            if !ip_part.is_empty() && ip_part != "0.0.0.0" {
                if let Ok(ip) = ip_part.parse::<std::net::IpAddr>() {
                    return SocketAddr::new(ip, port);
                }
            }
            return SocketAddr::from(([0, 0, 0, 0], port));
        }
    }

    // 默认回退到9000端口
    SocketAddr::from(([0, 0, 0, 0], 9000))
}

/// 启动Web服务
async fn start_web_server(
    config_manager: Arc<ConfigManager>,
    addr: SocketAddr,
    use_https: bool,
) -> anyhow::Result<()> {
    use handlers::account_handler::profile_handler;
    use handlers::connector_handler::{
        search_connector_get, search_connector_options, search_connector_post,
    };
    use handlers::datasource_handler::{
        search_datasource_get, search_datasource_options, search_datasource_post,
    };
    use handlers::info_handler::{health_handler, info_handler};
    use handlers::mcp_server_handler::{
        search_mcp_server_get, search_mcp_server_options, search_mcp_server_post,
    };
    use handlers::static_handler::static_file_handler;

    // 创建CORS层，允许所有来源、方法和头部
    let cors_layer = CorsLayer::very_permissive();

    // 构建Web应用路由（用于提供Web界面）
    let api_routes = Router::new()
        .route("/health", axum::routing::get(health_handler))
        .route("/_info", axum::routing::get(info_handler))
        .route("/provider/_info", axum::routing::get(info_handler))
        .route("/account/profile", axum::routing::get(profile_handler))
        .route(
            "/datasource/_search",
            axum::routing::get(search_datasource_get)
                .post(search_datasource_post)
                .options(search_datasource_options),
        )
        .route(
            "/connector/_search",
            axum::routing::get(search_connector_get)
                .post(search_connector_post)
                .options(search_connector_options),
        )
        .route(
            "/mcp_server/_search",
            axum::routing::get(search_mcp_server_get)
                .post(search_mcp_server_post)
                .options(search_mcp_server_options),
        );

    // SSO路由需要在静态路由之前，避免被fallback捕获
    let sso_routes = Router::new().route(
        "/sso/login/cloud",
        axum::routing::get(handlers::sso_handler::sso_login_handler),
    );

    let static_routes = Router::new()
        .route("/", axum::routing::get(static_file_handler))
        .fallback(axum::routing::get(static_file_handler));

    // 为Web服务器创建轻量级AppState（不需要数据库连接）
    use crate::auth::token_blacklist::TokenBlacklist;
    let token_blacklist = Arc::new(TokenBlacklist::new());

    // 创建一个轻量级的AppState用于Web服务器
    // 注意：Web服务器不需要数据库连接，所以我们暂时保持使用ConfigManager
    let web_app = api_routes
        .merge(sso_routes) // SSO路由优先
        .merge(static_routes) // 静态路由最后，包含fallback
        .layer(cors_layer)
        .with_state(config_manager.clone());

    tracing::info!("Starting Web server on {}", addr);

    if use_https {
        tracing::warn!("HTTPS for Web server is not yet implemented, starting HTTP server instead");
    }

    // 运行HTTP服务器
    let server_result = axum::Server::bind(&addr)
        .serve(web_app.into_make_service())
        .await;

    match &server_result {
        Ok(_) => tracing::info!("Web HTTP server stopped gracefully"),
        Err(e) => tracing::error!("Web HTTP server failed: {}", e),
    }

    server_result.map_err(|e| anyhow::anyhow!("Failed to start Web HTTP server: {}", e))
}

/// 启动API服务
async fn start_api_server(
    config_manager: Arc<ConfigManager>,
    addr: SocketAddr,
    use_https: bool,
) -> anyhow::Result<()> {
    // 创建CORS层，允许所有来源、方法和头部
    let cors_layer = CorsLayer::very_permissive();

    // 构建API应用路由
    use crate::auth::token_blacklist::TokenBlacklist;
    use crate::handlers::account_handler::{
        change_password_handler, login_handler, logout_handler, profile_handler,
    };
    use crate::handlers::chat_handler::chat_handler;
    use crate::handlers::datasource_handler::{
        search_datasource_get, search_datasource_options, search_datasource_post,
    };
    use crate::handlers::info_handler::health_handler;
    use crate::handlers::models_handler::models_handler;
    use crate::handlers::token_handler::{
        delete_access_token_handler, get_token_stats_handler, list_access_tokens_handler,
        rename_access_token_handler, request_access_token_handler,
    };
    use crate::services::token_service::TokenService;

    // 初始化全局数据库连接
    use crate::database::{init_database, DB};
    use crate::repositories::token_repository::TokenRepository;

    tracing::info!("正在初始化全局数据库连接...");

    // ✅ 使用ConfigManager的数据库配置，而不是默认值
    let db_config = config_manager.get_database_config();
    tracing::info!("数据库配置获取成功");

    // 初始化全局数据库连接
    init_database(&db_config)
        .await
        .map_err(|e| anyhow::anyhow!("Failed to initialize database: {}", e))?;

    tracing::info!("全局数据库连接初始化成功");

    // 初始化数据库表结构（不需要迁移，直接定义）
    tracing::info!("正在初始化数据库表结构...");

    // 使用SurrealDB的DEFINE语句来创建表结构
    DB.query(
        "
        -- 定义DataSource表
        DEFINE TABLE IF NOT EXISTS datasource SCHEMALESS;
        DEFINE FIELD IF NOT EXISTS id ON TABLE datasource TYPE string;
        DEFINE FIELD IF NOT EXISTS name ON TABLE datasource TYPE string;
        DEFINE FIELD IF NOT EXISTS description ON TABLE datasource TYPE option<string>;
        DEFINE FIELD IF NOT EXISTS connector_config ON TABLE datasource TYPE object;
        DEFINE FIELD IF NOT EXISTS created_at ON TABLE datasource TYPE datetime DEFAULT time::now();
        DEFINE FIELD IF NOT EXISTS updated_at ON TABLE datasource TYPE datetime DEFAULT time::now();
        DEFINE FIELD IF NOT EXISTS deleted_at ON TABLE datasource TYPE option<datetime>;
        DEFINE INDEX IF NOT EXISTS datasource_name_unique ON TABLE datasource FIELDS name UNIQUE;

        -- 定义User表（用于认证）
        DEFINE TABLE IF NOT EXISTS user SCHEMALESS;
        DEFINE FIELD IF NOT EXISTS name ON TABLE user TYPE string;
        DEFINE FIELD IF NOT EXISTS pass ON TABLE user TYPE string;
        DEFINE INDEX IF NOT EXISTS unique_name ON TABLE user FIELDS name UNIQUE;

        -- 定义Token表
        DEFINE TABLE IF NOT EXISTS token SCHEMALESS;
        DEFINE FIELD IF NOT EXISTS user_id ON TABLE token TYPE string;
        DEFINE FIELD IF NOT EXISTS token_hash ON TABLE token TYPE string;
        DEFINE FIELD IF NOT EXISTS name ON TABLE token TYPE string;
        DEFINE FIELD IF NOT EXISTS created_at ON TABLE token TYPE datetime DEFAULT time::now();
        DEFINE FIELD IF NOT EXISTS expires_at ON TABLE token TYPE option<datetime>;
        DEFINE FIELD IF NOT EXISTS last_used_at ON TABLE token TYPE option<datetime>;
        ",
    )
    .await
    .map_err(|e| anyhow::anyhow!("Failed to initialize database schema: {}", e))?;

    tracing::info!("数据库表结构初始化完成");

    // 创建TokenRepository实例（使用全局DB连接）
    let token_repository = Arc::new(TokenRepository::new_with_global_db());

    // 创建TokenService实例
    let token_service = Arc::new(TokenService::new(token_repository.clone()));

    // 创建TokenBlacklist实例
    let token_blacklist = Arc::new(TokenBlacklist::new());

    // 创建应用状态（不再需要db_client）
    use crate::app_state::AppState;
    let app_state = AppState::new_with_global_db(
        config_manager.clone(),
        token_repository.clone(),
        token_service.clone(),
        token_blacklist.clone(),
    );

    // 创建不需要认证的路由（使用AppState）
    let public_routes = Router::new()
        .route("/_info", get(info_handler))
        .route("/health", get(health_handler)) // 健康检查端点
        .route("/account/login", post(login_handler)) // 登录端点
        .route("/setup/_initialize", post(initialize_handler))
        .with_state(config_manager.clone());

    // 创建需要认证的路由（使用ConfigManager）
    let protected_routes = Router::new()
        .route("/account/profile", get(profile_handler)) // 用户配置文件端点
        .route(
            "/account/password",
            axum::routing::put(change_password_handler),
        ) // 密码修改端点
        .route("/api/v1/models", get(models_handler))
        .route("/api/v1/chat", post(chat_handler))
        .route("/ws", axum::routing::get(websocket_handler))
        // DataSource API 路由
        .route("/datasource/_search", get(search_datasource_get))
        .route("/datasource/_search", post(search_datasource_post))
        .route(
            "/datasource/_search",
            axum::routing::options(search_datasource_options),
        )
        .route_layer(axum::middleware::from_fn_with_state(
            app_state.clone(),
            auth_middleware,
        ))
        .with_state(config_manager.clone());

    // 创建API令牌管理路由（需要认证）
    let token_routes = Router::new()
        // 注销端点
        .route("/account/logout", post(logout_handler))
        // API令牌管理端点
        .route(
            "/auth/request_access_token",
            post(request_access_token_handler),
        )
        .route("/auth/access_token/_cat", get(list_access_tokens_handler))
        .route("/auth/access_token/_stats", get(get_token_stats_handler))
        .route(
            "/auth/access_token/:token_id",
            delete(delete_access_token_handler),
        )
        .route(
            "/auth/access_token/:token_id/_rename",
            post(rename_access_token_handler),
        )
        .route_layer(axum::middleware::from_fn_with_state(
            app_state.clone(),
            auth_middleware,
        ))
        .with_state(app_state.clone());

    // 合并所有路由器
    let app = public_routes
        .merge(protected_routes)
        .merge(token_routes)
        .layer(cors_layer);

    tracing::info!("Starting API server on {}", addr);

    if use_https {
        tracing::warn!("HTTPS for API server is not yet implemented, starting HTTP server instead");
    }

    // 运行HTTP服务器
    let server_result = axum::Server::bind(&addr)
        .serve(app.into_make_service())
        .await;

    match &server_result {
        Ok(_) => tracing::info!("API HTTP server stopped gracefully"),
        Err(e) => tracing::error!("API HTTP server failed: {}", e),
    }

    server_result.map_err(|e| anyhow::anyhow!("Failed to start API HTTP server: {}", e))
}

#[cfg(test)]
mod tests {
    use super::*;
    use axum::{
        body::Body,
        http::{Request, StatusCode},
        routing::get,
    };
    use tower::ServiceExt;
    use tower_http::cors::CorsLayer;

    #[tokio::test]
    async fn test_cors_headers_present() {
        // 创建一个简单的测试路由，不需要配置管理器
        let app = Router::new()
            .route("/_info", get(|| async { "Info endpoint" }))
            .layer(CorsLayer::very_permissive());

        // 创建一个带有Origin头部的请求（模拟跨域请求）
        let request = Request::builder()
            .uri("/_info")
            .header("origin", "http://localhost:3000")
            .body(Body::empty())
            .unwrap();

        // 发送请求
        let response = app.oneshot(request).await.unwrap();

        // 验证响应状态码
        assert_eq!(response.status(), StatusCode::OK);

        // 验证CORS头部是否存在
        assert!(response
            .headers()
            .contains_key("access-control-allow-origin"));
    }
}
