use crate::auth::token_blacklist::TokenBlacklist;
use crate::config::config_manager::ConfigManager;
use crate::database::SurrealDBClient;
use crate::repositories::token_repository::TokenRepository;
use crate::services::token_service::TokenService;
use std::sync::Arc;

/// 应用状态结构
#[derive(Clone)]
pub struct AppState {
    pub config_manager: Arc<ConfigManager>,
    pub db_client: Arc<SurrealDBClient>,
    pub token_repository: Arc<TokenRepository>,
    pub token_service: Arc<TokenService>,
    pub token_blacklist: Arc<TokenBlacklist>,
}

impl AppState {
    pub fn new(
        config_manager: Arc<ConfigManager>,
        db_client: Arc<SurrealDBClient>,
        token_repository: Arc<TokenRepository>,
        token_service: Arc<TokenService>,
        token_blacklist: Arc<TokenBlacklist>,
    ) -> Self {
        Self {
            config_manager,
            db_client,
            token_repository,
            token_service,
            token_blacklist,
        }
    }
}
